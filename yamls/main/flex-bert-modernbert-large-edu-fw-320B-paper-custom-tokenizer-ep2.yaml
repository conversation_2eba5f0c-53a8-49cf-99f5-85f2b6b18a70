# ModernBert Configuration
# This configuration implements the ModernBert paper features:
# 1. Bias Terms Disabled: All linear layers except final decoder have bias=false
# 2. GeGLU Activation: Using GLU MLP layer for GeGLU-like behavior  
# 3. Alternating Local/Global Attention: Every 3rd layer uses global attention (RoPE theta=160k)
#    Remaining layers use 128-token sliding window (RoPE theta=10k)
# 4. Trapezoidal Learning Rate: Warmup-Stable-Decay schedule with peak LR=8e-4
# 5. Batch Size Schedule: Warmup from 768 to 4096 over first 2.5% of training

# Follow the instructions in the README to set up ./my-copy-c4
# Or point data paths to your remote C4 dataset
data_local: /s2_nfs/mds_300BT_corpus/
data_remote: # If blank, files must be present in data_local

max_seq_len: 1024
tokenizer_name: "/home/<USER>/notebooks/tokenizer_spm_50368_140B_EnFa_hf_edited" # switch to bert tokenizer until we add [MASK] token to the llama tokenizer meta-llama/Llama-2-7b-hf
mlm_probability: 0.3 # FlexBERT should use 30% masking for optimal performance

# Run Name
run_name: flex-bert-modernbert-large-edu-fw-320B-article-custom-tokenizer-ep2

# Model
model:
  name: flex_bert
  recompute_metric_loss: false # recompute metric loss, use if passing label_smoothing to record non-label-smoothed loss as a metric
  pretrained_model_name: "/home/<USER>/notebooks/bert" # ${tokenizer_name}
  tokenizer_name: ${tokenizer_name}
  # FlexBERT 'base' generally uses the default architecture values from the Hugging Face BertConfig object
  # Note: if using the pretrained_checkpoint argument to create a model from an existing checkpoint, make sure
  # the model_config settings match the architecture of the existing model
  model_config:
    num_attention_heads: 16 # bert-base default
    num_hidden_layers: 28
    attention_layer: rope  # Using RoPE for alternating local/global attention
    attention_probs_dropout_prob: 0.0
    # Disable bias terms in all linear layers except final decoder
    hidden_size: 1024
    intermediate_size: 2624
    attn_out_bias: false
    attn_out_dropout_prob: 0.1
    attn_qkv_bias: false
    bert_layer: prenorm
    embed_dropout_prob: 0.0
    embed_norm: false
    final_norm: true
    embedding_layer: sans_pos
    loss_function: fa_cross_entropy
    loss_kwargs:
      reduction: mean
    mlp_dropout_prob: 0.0
    mlp_in_bias: false
    mlp_layer: glu  # Using GLU (GeGLU-like) activation as specified
    mlp_out_bias: false
    normalization: layernorm
    norm_kwargs:
      eps: 1e-5
      bias: false  # Disable bias in LayerNorms
    padding: unpadded 
    sparse_prediction: false
    hidden_act: gelu
    init_method: full_megatron
    init_std: 0.02
    init_cutoff_factor: 2.0
    init_small_embedding: false
    deterministic_fa2: false
    initial_attention_layer: null
    initial_bert_layer: null
    initial_mlp_layer: null
    num_initial_layers: 0
    skip_first_prenorm: true
    # Alternating local/global attention settings
    sliding_window: 128  # 128 token local sliding window
    global_attn_every_n_layers: 3  # Every third layer uses global attention
    rotary_emb_base: 160000.0  # RoPE theta for global attention layers
    local_attn_rotary_emb_base: 10000.0  # RoPE theta for local attention layers
    unpad_embeddings: false
    pad_logits: false
    # Enable bias only for final decoder layer
    decoder_bias: true
    allow_embedding_resizing: true


# Dataloaders
train_loader:
  name: text
  dataset:
    streaming: false
    local: ${data_local}
    remote: ${data_remote}
    split: train
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: true
    mlm_probability: ${mlm_probability}
  drop_last: true
  num_workers: 8  # Adjust based on available RAM
  # sequence_packing: true
  # Batch size scheduling: warmup from 768 to 4096 over first 2.5% of training
  # batch_size_warmup_min_size: 768
  # batch_size_warmup_tokens: 0.025dur

eval_loader:
  name: text
  dataset:
    streaming: false
    local: ${data_local}
    remote: ${data_remote}
    split: test
    tokenizer_name: ${tokenizer_name}
    max_seq_len: ${max_seq_len}
    shuffle: false
    mlm_probability: 0.15 # We always evaluate at 15% masking for consistent comparison
  drop_last: true
  num_workers: 8
  # sequence_packing: true

# Optimization
# Learning Rate schedule adapted from ModernBERT paper's large model approach:
# Paper used: 2B warmup → 900B at 5e-4 → 800B at 5e-5 (total 1.7T)
# Scaled to 320B: 0.4B warmup → 169B at 5e-4 → 151B at 5e-5
scheduler:
  name: warmup_stable_decay
  t_warmup: 0.00125dur  
  t_decay: 0.4dur      
  alpha_f: 0.4          # Decay to 10% of peak LR (5e-5 when peak is 5e-4)

optimizer:
  name: decoupled_adamw
  lr: 5.0e-5  # Reduced learning rate for extended training (3rd epoch)
  betas:
  - 0.9
  - 0.98
  eps: 1.0e-06
  weight_decay: 1.0e-6  # Amount of weight decay regularization
  filter_bias_norm_wd: true  # If True, doesn't apply weight decay to norm layers and biases

# Batch size scheduling is implemented in the dataloader via SequencePacker
# See train_loader dataset configuration for batch_size_warmup_* parameters
algorithms:
  gradient_clipping:
    clipping_type: norm
    clipping_threshold: 1.0

max_duration: 2ep
eval_interval: 5000ba

# Initial batch size (will be increased by the batch_size_schedule)
global_train_batch_size: 4920 #4608 #4928 #4608 #2400 #3072 #4096
device_train_microbatch_size: 41 #64 #56 #96 # 96 #80 #64 #96 #128

# System
seed: 17
precision: amp_bf16

global_eval_batch_size: 512
device_eval_microbatch_size: 128
eval_subset_num_batches: 1024  # Only evaluate on 1024 batches

# Logging
progress_bar: false
log_to_console: true
console_log_interval: 10ba

callbacks:
  speed_monitor:
    window_size: 50
  lr_monitor: {}
  memory_monitor: {}
  optimizer_monitor: {}
  dataloader_speed: {}

loggers:
  tensorboard:
    log_dir: ~/tb_runs/${run_name}
    flush_interval: 100

  wandb:
    project: Modernbert_Pretrain
    # entity: sutkaggles

# Checkpointing
save_interval: 10000ba
save_num_checkpoints_to_keep: 20
save_folder: /s2_nfs/ckpt/{run_name}/
autoresume: true

# Override checkpoint's optimizer settings when resuming
restart_override: true